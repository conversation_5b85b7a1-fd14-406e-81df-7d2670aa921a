<script lang="ts">
	import { onMount } from 'svelte';
	import { highlights, type Highlight } from '$lib/stores/highlights';
	import { chatId, chats } from '$lib/stores';
	import { toast } from 'svelte-sonner';
	import Markdown from '$lib/components/chat/Messages/Markdown.svelte';

	export let onPasteText: (text: string) => void = () => {};
	export let show = false;

	let currentHighlights: Highlight[] = [];
	let groupedHighlights: Record<string, Highlight[]> = {};
	let expandedChats: Record<string, boolean> = {};

	// Subscribe to highlights store
	const unsubscribe = highlights.subscribe((allHighlights) => {
		currentHighlights = allHighlights;
		groupHighlightsByChat();
	});

	onMount(() => {
		// Load highlights when component mounts
		highlights.load();

		return () => {
			unsubscribe();
		};
	});

	function groupHighlightsByChat() {
		groupedHighlights = currentHighlights.reduce((acc, highlight) => {
			const chatKey = highlight.chatId || 'unknown';
			if (!acc[chatKey]) {
				acc[chatKey] = [];
			}
			acc[chatKey].push(highlight);
			return acc;
		}, {} as Record<string, Highlight[]>);

		// Auto-expand current chat
		if ($chatId && groupedHighlights[$chatId]) {
			expandedChats[$chatId] = true;
		}
	}

	function getChatTitle(chatKey: string): string {
		if (chatKey === 'unknown') return 'Unknown Chat';
		if (chatKey === $chatId) return 'Current Chat';

		// Try to find chat title from chats store
		const chat = $chats?.find(c => c.id === chatKey);
		return chat?.title || `Chat ${chatKey.substring(0, 8)}...`;
	}

	function toggleChatExpansion(chatKey: string) {
		expandedChats[chatKey] = !expandedChats[chatKey];
		expandedChats = { ...expandedChats }; // Trigger reactivity
	}

	async function pasteChatHighlights(chatKey: string) {
		const chatHighlights = groupedHighlights[chatKey] || [];
		if (chatHighlights.length === 0) {
			toast.error('No highlights to paste from this chat');
			return;
		}

		const text = chatHighlights
			.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
			.map(h => h.text)
			.join('\n\n');

		if (text.trim()) {
			onPasteText(text);
			const chatTitle = getChatTitle(chatKey);
			toast.success(`Highlights from ${chatTitle} pasted to input`);
		}
	}

	function removeHighlight(id: string) {
		highlights.remove(id);
		toast.success('Highlight removed');
	}

	function clearAllHighlights() {
		highlights.clear();
		toast.success('All highlights cleared');
	}

	function clearCurrentChatHighlights() {
		if ($chatId) {
			highlights.removeByChatId($chatId);
			toast.success('Current chat highlights cleared');
		}
	}

	function copyHighlight(text: string) {
		navigator.clipboard.writeText(text).then(() => {
			toast.success('Highlight copied to clipboard');
		}).catch(() => {
			toast.error('Failed to copy highlight');
		});
	}

	async function copyAllHighlights() {
		const text = await highlights.getAllText();
		if (text.trim()) {
			navigator.clipboard.writeText(text).then(() => {
				toast.success('All highlights copied to clipboard');
			}).catch(() => {
				toast.error('Failed to copy highlights');
			});
		} else {
			toast.error('No highlights to copy');
		}
	}

	function formatTimestamp(timestamp: string) {
		return new Date(timestamp).toLocaleString();
	}
</script>

{#if show}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] flex flex-col">
			<!-- Header -->
			<div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
				<h2 class="text-lg font-semibold text-gray-900 dark:text-white">
					All Highlights ({currentHighlights.length})
				</h2>
				<button
					on:click={() => (show = false)}
					class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Actions -->
			<div class="flex gap-2 p-4 border-b border-gray-200 dark:border-gray-700">
				<button
					on:click={copyAllHighlights}
					disabled={currentHighlights.length === 0}
					class="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
				>
					Copy All
				</button>
				<button
					on:click={clearCurrentChatHighlights}
					disabled={!$chatId || !groupedHighlights[$chatId]?.length}
					class="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
				>
					Clear Current Chat
				</button>
				<button
					on:click={clearAllHighlights}
					disabled={currentHighlights.length === 0}
					class="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
				>
					Clear All
				</button>
			</div>

			<!-- Highlights List -->
			<div class="flex-1 overflow-y-auto p-4">
				{#if currentHighlights.length === 0}
					<div class="text-center text-gray-500 dark:text-gray-400 py-8">
						<p>No highlights yet</p>
						<p class="text-sm mt-2">Select text in AI responses and click the highlight button to add highlights</p>
					</div>
				{:else}
					<div class="space-y-4">
						{#each Object.entries(groupedHighlights) as [chatKey, chatHighlights]}
							<div class="border border-gray-200 dark:border-gray-600 rounded-lg">
								<!-- Chat Header -->
								<div class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-t-lg">
									<button
										on:click={() => toggleChatExpansion(chatKey)}
										class="flex-1 flex items-center gap-2 p-3 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
									>
										<svg
											class="w-4 h-4 transition-transform {expandedChats[chatKey] ? 'rotate-90' : ''}"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
										</svg>
										<span class="font-medium text-gray-900 dark:text-white">
											{getChatTitle(chatKey)}
										</span>
										<span class="text-sm text-gray-500 dark:text-gray-400">
											({chatHighlights.length} highlight{chatHighlights.length !== 1 ? 's' : ''})
										</span>
									</button>

									<!-- Paste Button for this chat -->
									<button
										on:click={() => pasteChatHighlights(chatKey)}
										disabled={chatHighlights.length === 0}
										class="p-3 text-blue-500 hover:text-blue-700 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
										title="Paste highlights from this chat"
									>
										<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
										</svg>
									</button>
								</div>

								<!-- Chat Highlights -->
								{#if expandedChats[chatKey]}
									<div class="p-3 space-y-3 bg-white dark:bg-gray-800">
										{#each chatHighlights.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()) as highlight}
											<div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 group">
												<div class="flex items-start justify-between">
													<div class="flex-1">
														<div class="bg-yellow-200 dark:bg-yellow-800 px-2 py-1 rounded text-sm mb-2 inline-block">
															<div class="markdown-prose-xs">
																<Markdown id={`highlight-${highlight.id}`} content={highlight.text} />
															</div>
														</div>
														<div class="text-xs text-gray-500 dark:text-gray-400">
															{formatTimestamp(highlight.timestamp)}
														</div>
													</div>
													<div class="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
														<button
															on:click={() => copyHighlight(highlight.text)}
															class="text-blue-500 hover:text-blue-700 transition-colors"
															title="Copy highlight"
														>
															<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
															</svg>
														</button>
														<button
															on:click={() => removeHighlight(highlight.id)}
															class="text-red-500 hover:text-red-700 transition-colors"
															title="Remove highlight"
														>
															<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
															</svg>
														</button>
													</div>
												</div>
											</div>
										{/each}
									</div>
								{/if}
							</div>
						{/each}
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}
