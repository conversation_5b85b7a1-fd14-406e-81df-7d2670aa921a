<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { highlights, type Highlight, type NodePathElement } from '$lib/stores/highlights';
	import { toast } from 'svelte-sonner';
	import { normalizeText, shouldHighlightApplyToComponent, findHighlightPositionInComponent } from '$lib/utils/highlightFragmentation';
	import { shouldComponentBeHighlighted } from '$lib/utils/sourceBasedHighlighting';

	export let text: string;
	export let responseId: string;
	export let componentId: string = '';

	let highlightedSegments: Array<{
		text: string;
		isHighlighted: boolean;
		highlightId?: string;
	}> = [];

	let currentHighlights: Highlight[] = [];

	// Subscribe to highlights store
	const unsubscribe = highlights.subscribe((allHighlights) => {
		currentHighlights = allHighlights.filter(h => h.responseId === responseId);
		updateHighlightedSegments();
	});

	onDestroy(() => {
		unsubscribe();
	});

	onMount(() => {
		// Load highlights when component mounts
		highlights.load();
	});

	function updateHighlightedSegments() {
		if (!text || currentHighlights.length === 0) {
			highlightedSegments = [{ text, isHighlighted: false }];
			return;
		}

		// Check for exact matches first (with normalization)
		const exactMatch = currentHighlights.find(h =>
			normalizeText(h.text) === normalizeText(text)
		);
		if (exactMatch) {
			highlightedSegments = [{
				text: text,
				isHighlighted: true,
				highlightId: exactMatch.id
			}];
			return;
		}

		// Process source-based highlights first (new system)
		const sourceBasedHighlights = currentHighlights.filter(h => h.isSourceBased);
		for (const highlight of sourceBasedHighlights) {
			const mapping = shouldComponentBeHighlighted(componentId, text, highlight);
			if (mapping) {
				// Ensure the offsets are within bounds
				const startOffset = Math.max(0, Math.min(mapping.relativeStartOffset, text.length));
				const endOffset = Math.max(startOffset, Math.min(mapping.relativeEndOffset, text.length));

				// Apply source-based highlight
				const beforeText = text.substring(0, startOffset);
				const highlightedText = text.substring(startOffset, endOffset);
				const afterText = text.substring(endOffset);

				const segments = [];
				if (beforeText.trim()) segments.push({ text: beforeText, isHighlighted: false });
				if (highlightedText.trim()) segments.push({ text: highlightedText, isHighlighted: true, highlightId: highlight.id });
				if (afterText.trim()) segments.push({ text: afterText, isHighlighted: false });

				// If we have valid segments, use them
				if (segments.length > 0) {
					highlightedSegments = segments;
					return;
				}
			}
		}

		// Fall back to legacy highlighting system for backward compatibility
		const legacyHighlights = currentHighlights.filter(h => !h.isSourceBased);
		const applicableHighlights = legacyHighlights.filter(h =>
			shouldHighlightApplyToComponent(h, text, responseId)
		);

		if (applicableHighlights.length === 0) {
			highlightedSegments = [{ text, isHighlighted: false }];
			return;
		}

		// Handle highlighting by segmenting the text
		const segments = [];
		let currentIndex = 0;
		let hasHighlights = false;

		if (applicableHighlights.length === 0) {
			highlightedSegments = [{ text, isHighlighted: false }];
			return;
		}

		// Process applicable highlights with precise positioning
		for (const highlight of applicableHighlights) {
			// Find the exact position where this highlight should be applied
			const position = findHighlightPositionInComponent(highlight, text);

			if (position && position.startIndex >= currentIndex) {
				// Add non-highlighted text before this highlight
				if (position.startIndex > currentIndex) {
					const beforeText = text.substring(currentIndex, position.startIndex);
					if (beforeText.trim().length > 0) {
						segments.push({
							text: beforeText,
							isHighlighted: false
						});
					}
				}

				// Add the highlighted text (preserve original case from component)
				segments.push({
					text: position.text,
					isHighlighted: true,
					highlightId: highlight.id
				});

				currentIndex = position.endIndex;
				hasHighlights = true;

				// Only highlight the first occurrence to prevent over-highlighting
				break;
			}
		}

		// Add any remaining non-highlighted text
		if (currentIndex < text.length) {
			const remainingText = text.substring(currentIndex);
			if (remainingText.trim().length > 0) {
				segments.push({
					text: remainingText,
					isHighlighted: false
				});
			}
		}

		highlightedSegments = hasHighlights && segments.length > 0 ? segments : [{ text, isHighlighted: false }];
	}

	function removeHighlight(highlightId: string) {
		highlights.remove(highlightId);
		toast.success('Highlight removed');
	}
</script>

{#each highlightedSegments as segment}
	{#if segment.isHighlighted}
		<span class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded relative group inline-block">
			<span class="select-text">{segment.text.trim()}</span>
			<!-- X button that appears on hover, positioned absolutely to not affect layout -->
			<button
				on:click|stopPropagation={() => segment.highlightId && removeHighlight(segment.highlightId)}
				class="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 text-xs leading-none w-4 h-4 flex items-center justify-center rounded-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-red-100 dark:hover:bg-red-900 hover:border-red-300 dark:hover:border-red-600 shadow-sm z-10"
				title="Remove highlight"
			>
				×
			</button>
		</span>
	{:else}
		{segment.text}
	{/if}
{/each}
