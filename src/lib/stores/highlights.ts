import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import type { SourceHighlight, ComponentMapping } from '$lib/utils/sourceBasedHighlighting';

export interface HighlightFragment {
	id: string;
	parentHighlightId: string;
	text: string;
	startOffset: number;
	endOffset: number;
	globalStartOffset: number;
	globalEndOffset: number;
}

export interface NodePathElement {
	tagName: string;
	index: number;
	nodeType: number;
}

export interface Highlight {
	id: string;
	responseId: string;
	chatId?: string;
	text: string;
	timestamp: string;
	startOffset: number;
	endOffset: number;
	textNodeIndex?: number;
	parentElementId?: string;
	// Add a unique context identifier to prevent over-highlighting
	contextHash?: string;
	// Track which specific text instances this highlight applies to
	targetTextHash?: string;
	// Global offsets relative to the entire message content
	globalStartOffset?: number;
	globalEndOffset?: number;
	// Fragments for cross-paragraph highlights
	fragments?: HighlightFragment[];
	// New metadata for cross-paragraph matching
	globalStartIndex?: number;
	globalEndIndex?: number;
	fullMessageText?: string;
	// Precise DOM-based positioning for cross-paragraph highlights
	selectionId?: string;
	startPath?: NodePathElement[];
	endPath?: NodePathElement[];
	startContainerOffset?: number;
	endContainerOffset?: number;
	rawSelectedText?: string;
	// Source-based highlighting (new system)
	sourceStartOffset?: number;
	sourceEndOffset?: number;
	sourceText?: string;
	originalContent?: string;
	componentMappings?: ComponentMapping[];
	isSourceBased?: boolean;
}

const STORAGE_KEY = 'chat-highlights';

// Create a writable store for highlights
function createHighlightStore() {
	const { subscribe, set, update } = writable<Highlight[]>([]);

	return {
		subscribe,
		update,

		// Load highlights from localStorage
		load: () => {
			if (browser) {
				try {
					const stored = localStorage.getItem(STORAGE_KEY);
					const highlights = stored ? JSON.parse(stored) : [];
					set(highlights);
				} catch (error) {
					console.error('Failed to load highlights:', error);
					set([]);
				}
			}
		},

		// Save highlights to localStorage
		save: (highlights: Highlight[]) => {
			if (browser) {
				try {
					localStorage.setItem(STORAGE_KEY, JSON.stringify(highlights));
					set(highlights);
				} catch (error) {
					console.error('Failed to save highlights:', error);
				}
			}
		},

		// Add a new highlight
		add: (highlight: Omit<Highlight, 'id' | 'timestamp'>) => {
			const newHighlight: Highlight = {
				...highlight,
				id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
				timestamp: new Date().toISOString()
			};

			update(highlights => {
				const updated = [...highlights, newHighlight];
				if (browser) {
					localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
				}
				return updated;
			});

			return newHighlight;
		},

		// Remove a highlight by ID
		remove: (id: string) => {
			update(highlights => {
				const updated = highlights.filter(h => h.id !== id);
				if (browser) {
					localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
				}
				return updated;
			});
		},

		// Get highlights for a specific response
		getByResponseId: (responseId: string): Promise<Highlight[]> => {
			return new Promise((resolve) => {
				const unsubscribe = subscribe(highlights => {
					const filtered = highlights.filter(h => h.responseId === responseId);
					resolve(filtered);
					unsubscribe();
				});
			});
		},

		// Get highlights for a specific chat
		getByChatId: (chatId: string): Promise<Highlight[]> => {
			return new Promise((resolve) => {
				const unsubscribe = subscribe(highlights => {
					const filtered = highlights.filter(h => h.chatId === chatId);
					resolve(filtered);
					unsubscribe();
				});
			});
		},

		// Get highlights grouped by chat
		getGroupedByChat: (): Promise<Record<string, Highlight[]>> => {
			return new Promise((resolve) => {
				const unsubscribe = subscribe(highlights => {
					const grouped = highlights.reduce((acc, highlight) => {
						const chatId = highlight.chatId || 'unknown';
						if (!acc[chatId]) {
							acc[chatId] = [];
						}
						acc[chatId].push(highlight);
						return acc;
					}, {} as Record<string, Highlight[]>);
					resolve(grouped);
					unsubscribe();
				});
			});
		},

		// Remove highlights for a specific chat
		removeByChatId: (chatId: string) => {
			update(highlights => {
				const updated = highlights.filter(h => h.chatId !== chatId);
				if (browser) {
					localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
				}
				return updated;
			});
		},

		// Get all highlights as text for pasting
		getAllText: (): Promise<string> => {
			return new Promise((resolve) => {
				const unsubscribe = subscribe(highlights => {
					const text = highlights
						.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
						.map(h => h.text)
						.join('\n\n');
					resolve(text);
					unsubscribe();
				});
			});
		},

		// Clear all highlights
		clear: () => {
			if (browser) {
				localStorage.removeItem(STORAGE_KEY);
			}
			set([]);
		}
	};
}

export const highlights = createHighlightStore();
