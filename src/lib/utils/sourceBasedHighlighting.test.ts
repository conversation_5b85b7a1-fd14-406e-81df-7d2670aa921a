import { describe, it, expect } from 'vitest';
import { extractSourcePosition, createComponentMappings, shouldComponentBeHighlighted } from './sourceBasedHighlighting';
import { marked } from 'marked';

describe('Simplified Source-Based Highlighting', () => {
	describe('extractSourcePosition', () => {
		it('should find simple text in source content', () => {
			const originalContent = 'This is a test paragraph with some content.';
			const selectedText = 'test paragraph';

			// Mock selection object
			const mockSelection = {
				toString: () => selectedText,
				getRangeAt: () => ({})
			} as Selection;

			const mockContainer = {
				textContent: originalContent
			} as Element;

			const result = extractSourcePosition(mockSelection, mockContainer, originalContent);

			expect(result).toBeTruthy();
			expect(result?.text).toContain('test paragraph');
			expect(result?.startOffset).toBeGreaterThanOrEqual(0);
			expect(result?.endOffset).toBeGreaterThan(result?.startOffset);
		});

		it('should handle cross-paragraph text', () => {
			const originalContent = 'First paragraph content.\n\nSecond paragraph content.';
			const selectedText = 'paragraph content. Second paragraph';

			const mockSelection = {
				toString: () => selectedText,
				getRangeAt: () => ({})
			} as Selection;

			const mockContainer = {
				textContent: originalContent
			} as Element;

			const result = extractSourcePosition(mockSelection, mockContainer, originalContent);

			expect(result).toBeTruthy();
			expect(result?.text).toContain('paragraph');
		});

		it('should handle whitespace normalization', () => {
			const originalContent = 'Text   with    multiple    spaces.';
			const selectedText = 'Text with multiple spaces';

			const mockSelection = {
				toString: () => selectedText,
				getRangeAt: () => ({})
			} as Selection;

			const mockContainer = {
				textContent: originalContent
			} as Element;

			const result = extractSourcePosition(mockSelection, mockContainer, originalContent);

			expect(result).toBeTruthy();
		});

		it('should handle cross-paragraph text with newlines like the reported issue', () => {
			// This simulates the exact issue reported
			const originalContent = 'Some content before.\n\nPatience and Perseverance\n\nOnce upon a time, in a lush meadow with more content after.';
			const selectedText = 'Patience and Perseverance\n\nOnce upon a time, in a lush meadow';

			const mockSelection = {
				toString: () => selectedText,
				getRangeAt: () => ({})
			} as Selection;

			const mockContainer = {
				textContent: originalContent
			} as Element;

			const result = extractSourcePosition(mockSelection, mockContainer, originalContent);

			expect(result).toBeTruthy();
			expect(result?.text).toContain('Patience and Perseverance');
			expect(result?.text).toContain('Once upon a time');
		});

		it('should handle markdown processing differences with word-based matching', () => {
			// This simulates when selected text from rendered HTML doesn't exactly match markdown source
			const originalContent = '# Patience and Perseverance\n\nOnce upon a time, in a lush meadow filled with wildflowers, there lived a small ant named Andy.';
			const selectedText = 'Patience and Perseverance\n\nOnce upon a time, in a lush meadow'; // Missing the # from markdown

			const mockSelection = {
				toString: () => selectedText,
				getRangeAt: () => ({})
			} as Selection;

			const mockContainer = {
				textContent: originalContent
			} as Element;

			const result = extractSourcePosition(mockSelection, mockContainer, originalContent);

			expect(result).toBeTruthy();
			expect(result?.text).toContain('Patience and Perseverance');
			expect(result?.text).toContain('Once upon a time');
		});
	});

	describe('createComponentMappings', () => {
		it('should create mappings for simple text', () => {
			const sourceHighlight = {
				id: 'test-1',
				responseId: 'response-1',
				sourceStartOffset: 0,
				sourceEndOffset: 10,
				sourceText: 'test text',
				originalContent: 'test text content',
				timestamp: new Date().toISOString()
			};

			const tokens = marked.lexer('test text content');
			const mappings = createComponentMappings(sourceHighlight, tokens, 'response-1');

			expect(mappings).toBeDefined();
			expect(Array.isArray(mappings)).toBe(true);
		});

		it('should handle multiple paragraphs', () => {
			const sourceHighlight = {
				id: 'test-2',
				responseId: 'response-2',
				sourceStartOffset: 0,
				sourceEndOffset: 20,
				sourceText: 'first paragraph text',
				originalContent: 'first paragraph text\n\nsecond paragraph text',
				timestamp: new Date().toISOString()
			};

			const tokens = marked.lexer('first paragraph text\n\nsecond paragraph text');
			const mappings = createComponentMappings(sourceHighlight, tokens, 'response-2');

			expect(mappings).toBeDefined();
			expect(Array.isArray(mappings)).toBe(true);
		});
	});

	describe('shouldComponentBeHighlighted', () => {
		it('should identify components that should be highlighted', () => {
			const componentMappings = [
				{
					componentId: 'response-1-0',
					tokenIndex: 0,
					tokenType: 'paragraph',
					relativeStartOffset: 0,
					relativeEndOffset: 10,
					componentText: 'test text'
				}
			];

			const sourceHighlight = {
				componentMappings: componentMappings
			};

			const result = shouldComponentBeHighlighted(
				'response-1-0',
				'test text content',
				sourceHighlight
			);

			expect(result).toBeTruthy();
			expect(result?.componentId).toBe('response-1-0');
		});

		it('should handle partial component ID matches', () => {
			const componentMappings = [
				{
					componentId: 'response-1-0',
					tokenIndex: 0,
					tokenType: 'paragraph',
					relativeStartOffset: 0,
					relativeEndOffset: 10,
					componentText: 'test text'
				}
			];

			const sourceHighlight = {
				componentMappings: componentMappings
			};

			const result = shouldComponentBeHighlighted(
				'response-1-0-extra',
				'test text content',
				sourceHighlight
			);

			expect(result).toBeTruthy();
		});

		it('should return null for non-matching components', () => {
			const componentMappings = [
				{
					componentId: 'response-1-0',
					tokenIndex: 0,
					tokenType: 'paragraph',
					relativeStartOffset: 0,
					relativeEndOffset: 10,
					componentText: 'different text'
				}
			];

			const sourceHighlight = {
				componentMappings: componentMappings
			};

			const result = shouldComponentBeHighlighted(
				'response-2-0',
				'test text content',
				sourceHighlight
			);

			expect(result).toBeNull();
		});
	});
});
