import type { Highlight, HighlightFragment } from '$lib/stores/highlights';

/**
 * Utility for handling cross-paragraph highlight fragmentation
 */

export interface TextSegment {
	text: string;
	startOffset: number;
	endOffset: number;
	componentId: string;
}

/**
 * Extract all text segments from a message's markdown content
 * This simulates how the text is broken down into HighlightableText components
 */
export function extractTextSegments(messageContent: string, responseId: string): TextSegment[] {
	// This is a simplified version - in reality, we'd need to parse markdown
	// and extract text segments exactly as they appear in HighlightableText components

	const segments: TextSegment[] = [];
	const lines = messageContent.split('\n');
	let currentOffset = 0;

	lines.forEach((line, lineIndex) => {
		if (line.trim()) {
			const segment: TextSegment = {
				text: line,
				startOffset: currentOffset,
				endOffset: currentOffset + line.length,
				componentId: `${responseId}-${lineIndex}`
			};
			segments.push(segment);
		}
		currentOffset += line.length + 1; // +1 for newline
	});

	return segments;
}

/**
 * Create fragments for a highlight that may span multiple text segments
 */
export function createHighlightFragments(
	highlight: Highlight,
	textSegments: TextSegment[]
): HighlightFragment[] {
	const fragments: HighlightFragment[] = [];

	if (!highlight.globalStartOffset || !highlight.globalEndOffset) {
		return fragments;
	}

	// Find all text segments that intersect with the highlight
	for (const segment of textSegments) {
		const intersectionStart = Math.max(highlight.globalStartOffset, segment.startOffset);
		const intersectionEnd = Math.min(highlight.globalEndOffset, segment.endOffset);

		if (intersectionStart < intersectionEnd) {
			// Calculate relative offsets within the segment
			const relativeStart = intersectionStart - segment.startOffset;
			const relativeEnd = intersectionEnd - segment.startOffset;

			const fragmentText = segment.text.substring(relativeStart, relativeEnd);

			const fragment: HighlightFragment = {
				id: `${highlight.id}-fragment-${fragments.length}`,
				parentHighlightId: highlight.id,
				text: fragmentText,
				startOffset: relativeStart,
				endOffset: relativeEnd,
				globalStartOffset: intersectionStart,
				globalEndOffset: intersectionEnd
			};

			fragments.push(fragment);
		}
	}

	return fragments;
}

/**
 * Calculate global offsets for a text selection within a message
 */
export function calculateGlobalOffsets(
	selectedText: string,
	selectionRange: Range,
	messageContent: string
): { globalStartOffset: number; globalEndOffset: number } | null {
	try {
		// Get the text content of the entire message container
		const messageContainer = selectionRange.commonAncestorContainer;
		let rootContainer = messageContainer;

		// Find the root message container
		while (rootContainer.parentElement && !rootContainer.parentElement.id?.includes('message')) {
			rootContainer = rootContainer.parentElement;
		}

		const fullText = rootContainer.textContent || '';

		// Find the selected text within the full message text
		const selectedTextNormalized = selectedText.trim().replace(/\s+/g, ' ');
		const fullTextNormalized = fullText.replace(/\s+/g, ' ');

		const globalStart = fullTextNormalized.indexOf(selectedTextNormalized);
		if (globalStart === -1) {
			return null;
		}

		const globalEnd = globalStart + selectedTextNormalized.length;

		return {
			globalStartOffset: globalStart,
			globalEndOffset: globalEnd
		};
	} catch (error) {
		console.warn('Error calculating global offsets:', error);
		return null;
	}
}

/**
 * Check if a text segment contains any part of a highlight fragment
 */
export function findMatchingFragments(
	text: string,
	responseId: string,
	highlights: Highlight[]
): HighlightFragment[] {
	const matchingFragments: HighlightFragment[] = [];

	for (const highlight of highlights) {
		if (highlight.responseId !== responseId || !highlight.fragments) {
			continue;
		}

		for (const fragment of highlight.fragments) {
			// Check if this text segment contains the fragment text
			if (text.includes(fragment.text) && fragment.text.length > 2) {
				matchingFragments.push(fragment);
			}
		}
	}

	return matchingFragments;
}

/**
 * Normalize text for better matching across different renderings
 */
export function normalizeText(text: string): string {
	return text
		.trim()
		// Normalize whitespace (including newlines) to single spaces
		.replace(/\s+/g, ' ')
		// Normalize bullet points - convert various bullet formats to consistent format
		.replace(/^[•·‣⁃]\s*/gm, '• ')
		// Remove extra spaces around bullet points
		.replace(/\s*•\s*/g, '• ')
		.toLowerCase();
}

/**
 * Check if a highlight should apply to a specific text component
 * This is a more precise alternative to the broad text matching
 */
export function shouldHighlightApplyToComponent(
	highlight: any,
	componentText: string,
	responseId: string
): boolean {
	// Only process highlights for the same response
	if (highlight.responseId !== responseId) {
		return false;
	}

	// Skip very short highlights to prevent over-matching
	if (highlight.text.trim().length < 4) {
		return false;
	}

	// For highlights with precise positioning data (new system)
	if (highlight.selectionId && highlight.rawSelectedText) {
		const rawText = highlight.rawSelectedText.trim();
		// Check if this component contains the exact selected text
		return componentText.includes(rawText) && rawText.length >= 4;
	}

	// For traditional highlights, be more conservative
	const highlightText = highlight.text.trim();
	const normalizedHighlight = normalizeText(highlightText);
	const normalizedComponent = normalizeText(componentText);

	// Only match if the highlight text is completely contained
	if (normalizedComponent.includes(normalizedHighlight)) {
		const words = highlightText.split(/\s+/);
		if (words.length === 1) {
			// Single word - must be longer and relatively unique
			return highlightText.length > 8;
		} else {
			// Multi-word phrase - more likely to be intentional
			return highlightText.length > 6;
		}
	}

	return false;
}

/**
 * Find the exact position of highlighted text within a component
 * Returns null if the text should not be highlighted in this component
 */
export function findHighlightPositionInComponent(
	highlight: any,
	componentText: string
): { startIndex: number; endIndex: number; text: string } | null {
	let highlightText = highlight.text.trim();

	// Use raw selected text if available for more accurate matching
	if (highlight.rawSelectedText) {
		highlightText = highlight.rawSelectedText.trim();
	}

	// Find the highlight text in the component text (case-insensitive)
	const searchText = highlightText.toLowerCase();
	const componentTextLower = componentText.toLowerCase();

	const foundIndex = componentTextLower.indexOf(searchText);
	if (foundIndex === -1) {
		return null;
	}

	// Check if this is a word boundary match to avoid partial word matches
	const isWordStart = foundIndex === 0 || !/\w/.test(componentText[foundIndex - 1]);
	const isWordEnd = foundIndex + highlightText.length >= componentText.length ||
					  !/\w/.test(componentText[foundIndex + highlightText.length]);

	// For multi-word highlights, we don't need word boundary checks
	const isMultiWord = highlightText.includes(' ');

	if (isMultiWord || (isWordStart && isWordEnd)) {
		return {
			startIndex: foundIndex,
			endIndex: foundIndex + highlightText.length,
			text: componentText.substring(foundIndex, foundIndex + highlightText.length)
		};
	}

	return null;
}
