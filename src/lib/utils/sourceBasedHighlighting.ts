import type { Token } from 'marked';
import { marked } from 'marked';
import { replaceTokens, processResponseContent } from '$lib/utils';

/**
 * Source-based highlighting system that anchors highlights to the original markdown content
 * rather than the processed DOM representation.
 */

export interface SourceHighlight {
	id: string;
	responseId: string;
	chatId?: string;
	// Source-based positioning
	sourceStartOffset: number;
	sourceEndOffset: number;
	sourceText: string;
	originalContent: string;
	// Metadata
	timestamp: string;
	// Component mapping data
	componentMappings?: ComponentMapping[];
}

export interface ComponentMapping {
	componentId: string;
	tokenIndex: number;
	tokenType: string;
	relativeStartOffset: number;
	relativeEndOffset: number;
	componentText: string;
}

export interface SourcePosition {
	startOffset: number;
	endOffset: number;
	text: string;
}

/**
 * Extract the source position of a text selection from the original content
 * SIMPLIFIED VERSION: Uses direct text matching with minimal fallbacks
 */
export function extractSourcePosition(
	selection: Selection,
	messageContainer: Element,
	originalContent: string
): SourcePosition | null {
	try {
		const selectedText = selection.toString().trim();
		console.log('🔍 [HIGHLIGHT DEBUG] Starting source position extraction');
		console.log('🔍 [HIGHLIGHT DEBUG] Selected text:', JSON.stringify(selectedText));
		console.log('🔍 [HIGHLIGHT DEBUG] Original content length:', originalContent.length);

		if (!selectedText) {
			console.log('🔍 [HIGHLIGHT DEBUG] No selected text found');
			return null;
		}

		// ENHANCED APPROACH: Multiple matching strategies for better reliability
		console.log('🔍 [HIGHLIGHT DEBUG] Original content sample:', JSON.stringify(originalContent.substring(0, 200)));

		// Strategy 1: Direct match with original whitespace
		const directMatch = originalContent.indexOf(selectedText);
		if (directMatch !== -1) {
			console.log('🔍 [HIGHLIGHT DEBUG] Found direct match with original whitespace at:', directMatch);
			return {
				startOffset: directMatch,
				endOffset: directMatch + selectedText.length,
				text: selectedText
			};
		}

		// Strategy 2: Normalize whitespace for better matching
		const normalizedSelected = selectedText.replace(/\s+/g, ' ').trim();
		const normalizedOriginal = originalContent.replace(/\s+/g, ' ');

		console.log('🔍 [HIGHLIGHT DEBUG] Normalized selected:', JSON.stringify(normalizedSelected));
		console.log('🔍 [HIGHLIGHT DEBUG] Normalized original sample:', JSON.stringify(normalizedOriginal.substring(0, 200)));

		const normalizedMatch = normalizedOriginal.indexOf(normalizedSelected);
		if (normalizedMatch !== -1) {
			const sourceEndIndex = normalizedMatch + normalizedSelected.length;
			console.log('🔍 [HIGHLIGHT DEBUG] Found normalized match at:', normalizedMatch, '-', sourceEndIndex);

			return {
				startOffset: normalizedMatch,
				endOffset: sourceEndIndex,
				text: normalizedOriginal.substring(normalizedMatch, sourceEndIndex)
			};
		}

		// Strategy 3: Try to find the text by splitting into words and finding a sequence
		const selectedWords = selectedText.trim().split(/\s+/).filter(word => word.length > 2);
		console.log('🔍 [HIGHLIGHT DEBUG] Selected words:', selectedWords);

		if (selectedWords.length >= 2) {
			const firstWord = selectedWords[0];
			const lastWord = selectedWords[selectedWords.length - 1];

			// Find first word in normalized content
			const normalizedFirstWord = firstWord.replace(/[^\w]/g, '');
			const normalizedLastWord = lastWord.replace(/[^\w]/g, '');

			// Create a regex to find the first word (case insensitive, allowing for punctuation)
			const firstWordRegex = new RegExp(normalizedFirstWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
			const firstWordMatch = originalContent.match(firstWordRegex);

			if (firstWordMatch) {
				const firstWordIndex = firstWordMatch.index!;

				// Look for last word after first word
				const searchStart = firstWordIndex + firstWordMatch[0].length;
				const lastWordRegex = new RegExp(normalizedLastWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
				const remainingContent = originalContent.substring(searchStart);
				const lastWordMatch = remainingContent.match(lastWordRegex);

				if (lastWordMatch) {
					const lastWordIndex = searchStart + lastWordMatch.index!;
					const endIndex = lastWordIndex + lastWordMatch[0].length;
					const foundText = originalContent.substring(firstWordIndex, endIndex);

					// Verify this contains most of our selected words
					const foundWords = foundText.split(/\s+/).filter(word => word.length > 2);
					const matchingWords = selectedWords.filter(word =>
						foundWords.some(fw => {
							const normalizedFw = fw.replace(/[^\w]/g, '').toLowerCase();
							const normalizedWord = word.replace(/[^\w]/g, '').toLowerCase();
							return normalizedFw.includes(normalizedWord) || normalizedWord.includes(normalizedFw);
						})
					);

					if (matchingWords.length >= selectedWords.length * 0.6) { // 60% word match threshold
						console.log('🔍 [HIGHLIGHT DEBUG] Found word-based match at:', firstWordIndex, '-', endIndex);
						console.log('🔍 [HIGHLIGHT DEBUG] Matched', matchingWords.length, 'of', selectedWords.length, 'words');
						console.log('🔍 [HIGHLIGHT DEBUG] Found text sample:', JSON.stringify(foundText.substring(0, 100)));

						return {
							startOffset: firstWordIndex,
							endOffset: endIndex,
							text: foundText
						};
					}
				}
			}
		}

		// Strategy 4: Try fuzzy matching with smaller chunks
		if (selectedWords.length >= 3) {
			// Try to find a sequence of 3-4 consecutive words
			for (let i = 0; i <= selectedWords.length - 3; i++) {
				const chunk = selectedWords.slice(i, i + 3).join(' ');
				const normalizedChunk = chunk.replace(/\s+/g, ' ').trim();

				// Try to find this chunk in the original content
				const chunkIndex = normalizedOriginal.indexOf(normalizedChunk);
				if (chunkIndex !== -1) {
					// Found a chunk, try to expand it to cover more of the selection
					let expandedStart = chunkIndex;
					let expandedEnd = chunkIndex + normalizedChunk.length;

					// Try to expand backwards to include earlier words
					for (let j = i - 1; j >= 0; j--) {
						const prevWord = selectedWords[j];
						const beforeChunk = normalizedOriginal.substring(Math.max(0, expandedStart - 50), expandedStart);
						if (beforeChunk.toLowerCase().includes(prevWord.toLowerCase())) {
							const wordStart = beforeChunk.toLowerCase().lastIndexOf(prevWord.toLowerCase());
							if (wordStart !== -1) {
								expandedStart = Math.max(0, expandedStart - 50) + wordStart;
							}
						} else {
							break;
						}
					}

					// Try to expand forwards to include later words
					for (let j = i + 3; j < selectedWords.length; j++) {
						const nextWord = selectedWords[j];
						const afterChunk = normalizedOriginal.substring(expandedEnd, expandedEnd + 50);
						if (afterChunk.toLowerCase().includes(nextWord.toLowerCase())) {
							const wordEnd = afterChunk.toLowerCase().indexOf(nextWord.toLowerCase()) + nextWord.length;
							expandedEnd = expandedEnd + wordEnd;
						} else {
							break;
						}
					}

					const expandedText = normalizedOriginal.substring(expandedStart, expandedEnd);
					console.log('🔍 [HIGHLIGHT DEBUG] Found fuzzy match at:', expandedStart, '-', expandedEnd);
					console.log('🔍 [HIGHLIGHT DEBUG] Expanded text sample:', JSON.stringify(expandedText.substring(0, 100)));

					return {
						startOffset: expandedStart,
						endOffset: expandedEnd,
						text: expandedText
					};
				}
			}
		}

		console.log('🔍 [HIGHLIGHT DEBUG] No match found in source content with any strategy');
		return null;
	} catch (error) {
		console.error('🔍 [HIGHLIGHT DEBUG] Error extracting source position:', error);
		return null;
	}
}

// REMOVED: Complex mapRenderedToSource function - using direct matching instead

/**
 * Normalize text for mapping between source and rendered content
 */
function normalizeForMapping(text: string): string {
	return text
		.trim()
		// Normalize whitespace
		.replace(/\s+/g, ' ')
		// Remove markdown formatting that gets processed away
		.replace(/\*\*(.*?)\*\*/g, '$1') // Bold
		.replace(/\*(.*?)\*/g, '$1') // Italic
		.replace(/`(.*?)`/g, '$1') // Code spans
		// Normalize bullet points
		.replace(/^[•·‣⁃-]\s*/gm, '• ');
}

// REMOVED: Complex findTextIntersection function - using simple string matching instead

/**
 * Create component mappings for a source highlight
 * SIMPLIFIED VERSION: Uses direct text matching without complex intersection logic
 */
export function createComponentMappings(
	sourceHighlight: SourceHighlight,
	tokens: Token[],
	responseId: string
): ComponentMapping[] {
	const mappings: ComponentMapping[] = [];
	const highlightedText = sourceHighlight.sourceText || '';

	console.log('🔍 [HIGHLIGHT DEBUG] Creating component mappings');
	console.log('🔍 [HIGHLIGHT DEBUG] Highlighted text:', JSON.stringify(highlightedText));
	console.log('🔍 [HIGHLIGHT DEBUG] Number of tokens:', tokens.length);

	// SIMPLIFIED APPROACH: Process tokens with simple text matching
	function processTokens(tokenList: Token[], parentId: string = responseId, depth: number = 0): void {
		tokenList.forEach((token, tokenIndex) => {
			const componentId = `${parentId}-${tokenIndex}`;
			const tokenText = extractTokenText(token);

			if (!tokenText.trim()) return;

			// Simple text matching - check if token contains any part of the highlighted text
			const normalizedTokenText = tokenText.trim().replace(/\s+/g, ' ');
			const normalizedHighlightText = highlightedText.trim().replace(/\s+/g, ' ');

			console.log('🔍 [HIGHLIGHT DEBUG] Checking token:', componentId, JSON.stringify(normalizedTokenText.substring(0, 50)));

			// Check for overlap using simple string operations
			let hasOverlap = false;
			let relativeStart = 0;
			let relativeEnd = tokenText.length;
			let componentText = '';

			// Case 1: Token text is completely contained in highlight
			if (normalizedHighlightText.includes(normalizedTokenText)) {
				hasOverlap = true;
				relativeStart = 0;
				relativeEnd = tokenText.length;
				componentText = tokenText;
				console.log('🔍 [HIGHLIGHT DEBUG] Token completely in highlight');
			}
			// Case 2: Highlight is completely contained in token
			else if (normalizedTokenText.includes(normalizedHighlightText)) {
				hasOverlap = true;
				const startIndex = normalizedTokenText.indexOf(normalizedHighlightText);
				if (startIndex !== -1) {
					relativeStart = startIndex;
					relativeEnd = startIndex + normalizedHighlightText.length;
					componentText = tokenText.substring(relativeStart, relativeEnd);
					console.log('🔍 [HIGHLIGHT DEBUG] Highlight completely in token at:', startIndex);
				}
			}
			// Case 3: Partial overlap - check if any words match
			else {
				const highlightWords = normalizedHighlightText.split(' ').filter(w => w.length > 2);
				const tokenWords = normalizedTokenText.split(' ');

				for (const word of highlightWords) {
					if (tokenWords.some(tw => tw.includes(word) || word.includes(tw))) {
						hasOverlap = true;
						// For partial matches, highlight the whole token for simplicity
						relativeStart = 0;
						relativeEnd = tokenText.length;
						componentText = tokenText;
						console.log('🔍 [HIGHLIGHT DEBUG] Partial word match found:', word);
						break;
					}
				}
			}

			if (hasOverlap && componentText.trim()) {
				const mapping = {
					componentId: componentId,
					tokenIndex: tokenIndex,
					tokenType: token.type,
					relativeStartOffset: relativeStart,
					relativeEndOffset: relativeEnd,
					componentText: componentText.trim()
				};
				mappings.push(mapping);
				console.log('🔍 [HIGHLIGHT DEBUG] Added mapping:', mapping);
			}

			// Process nested tokens
			if (token.tokens && Array.isArray(token.tokens)) {
				processTokens(token.tokens, componentId, depth + 1);
			}
		});
	}

	processTokens(tokens);
	return mappings;
}

/**
 * Extract text content from a token recursively
 */
function extractTokenText(token: Token): string {
	// Handle different token types
	if (token.type === 'text') {
		return token.text || '';
	} else if (token.type === 'escape') {
		return token.text || '';
	} else if (token.type === 'codespan') {
		return token.text || '';
	} else if (token.type === 'br') {
		return '\n';
	} else if (token.type === 'space') {
		return ' ';
	} else if (token.tokens && Array.isArray(token.tokens)) {
		// Recursively extract text from nested tokens
		return token.tokens.map(t => extractTokenText(t)).join('');
	} else if ('text' in token && token.text) {
		return token.text;
	} else if ('raw' in token && token.raw) {
		return token.raw;
	}

	return '';
}

/**
 * Check if a component should be highlighted based on source mappings
 * SIMPLIFIED VERSION: Uses straightforward text matching with debugging
 */
export function shouldComponentBeHighlighted(
	componentId: string,
	componentText: string,
	sourceHighlight: any
): ComponentMapping | null {
	console.log('🔍 [HIGHLIGHT DEBUG] Checking component:', componentId, JSON.stringify(componentText.substring(0, 50)));

	if (!sourceHighlight.componentMappings) {
		console.log('🔍 [HIGHLIGHT DEBUG] No component mappings found');
		return null;
	}

	// SIMPLIFIED APPROACH: Find exact component ID match first
	let mapping = sourceHighlight.componentMappings.find(m => m.componentId === componentId);

	if (!mapping) {
		// Try partial matches for component IDs
		mapping = sourceHighlight.componentMappings.find(m =>
			componentId.includes(m.componentId) || m.componentId.includes(componentId)
		);
	}

	if (mapping) {
		console.log('🔍 [HIGHLIGHT DEBUG] Found mapping:', mapping);

		// Simple text verification - check if the mapping text appears in the component
		const normalizedComponentText = componentText.trim().replace(/\s+/g, ' ');
		const normalizedMappingText = mapping.componentText.trim().replace(/\s+/g, ' ');

		// Check if mapping text is contained in component text
		if (normalizedComponentText.includes(normalizedMappingText)) {
			const actualStartOffset = normalizedComponentText.indexOf(normalizedMappingText);
			console.log('🔍 [HIGHLIGHT DEBUG] Text match found at offset:', actualStartOffset);

			return {
				...mapping,
				relativeStartOffset: actualStartOffset,
				relativeEndOffset: actualStartOffset + normalizedMappingText.length
			};
		}

		console.log('🔍 [HIGHLIGHT DEBUG] Text verification failed');
		console.log('🔍 [HIGHLIGHT DEBUG] Expected:', JSON.stringify(normalizedMappingText));
		console.log('🔍 [HIGHLIGHT DEBUG] Actual:', JSON.stringify(normalizedComponentText));
	} else {
		console.log('🔍 [HIGHLIGHT DEBUG] No mapping found for component ID');
	}

	return null;
}

/**
 * Get the tokens for a given content (same processing as the main pipeline)
 */
export function getTokensForContent(content: string, sourceIds: string[] = [], modelName?: string, userName?: string): Token[] {
	const processedContent = replaceTokens(processResponseContent(content), sourceIds, modelName, userName);
	return marked.lexer(processedContent);
}
