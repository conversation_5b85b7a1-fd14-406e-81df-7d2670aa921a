import type { Token } from 'marked';
import { marked } from 'marked';
import { replaceTokens, processResponseContent } from '$lib/utils';

/**
 * Source-based highlighting system that anchors highlights to the original markdown content
 * rather than the processed DOM representation.
 */

export interface SourceHighlight {
	id: string;
	responseId: string;
	chatId?: string;
	// Source-based positioning
	sourceStartOffset: number;
	sourceEndOffset: number;
	sourceText: string;
	originalContent: string;
	// Metadata
	timestamp: string;
	// Component mapping data
	componentMappings?: ComponentMapping[];
}

export interface ComponentMapping {
	componentId: string;
	tokenIndex: number;
	tokenType: string;
	relativeStartOffset: number;
	relativeEndOffset: number;
	componentText: string;
}

export interface SourcePosition {
	startOffset: number;
	endOffset: number;
	text: string;
}

/**
 * Extract the source position of a text selection from the original content
 */
export function extractSourcePosition(
	selection: Selection,
	messageContainer: Element,
	originalContent: string
): SourcePosition | null {
	try {
		const range = selection.getRangeAt(0);
		const selectedText = selection.toString().trim();

		if (!selectedText) {
			return null;
		}

		// Get the full text content of the message container
		const fullRenderedText = messageContainer.textContent || '';

		// Find where the selected text appears in the rendered content
		const renderedStartOffset = fullRenderedText.indexOf(selectedText);
		if (renderedStartOffset === -1) {
			return null;
		}

		// Map the rendered position back to the source content
		const sourcePosition = mapRenderedToSource(
			renderedStartOffset,
			renderedStartOffset + selectedText.length,
			originalContent,
			fullRenderedText
		);

		if (sourcePosition) {
			return {
				startOffset: sourcePosition.startOffset,
				endOffset: sourcePosition.endOffset,
				text: originalContent.substring(sourcePosition.startOffset, sourcePosition.endOffset)
			};
		}

		return null;
	} catch (error) {
		console.warn('Error extracting source position:', error);
		return null;
	}
}

/**
 * Map rendered text positions back to source content positions
 */
function mapRenderedToSource(
	renderedStart: number,
	renderedEnd: number,
	originalContent: string,
	renderedContent: string
): { startOffset: number; endOffset: number } | null {
	// Get the selected text from the rendered content
	const renderedText = renderedContent.substring(renderedStart, renderedEnd);

	// Clean up the text for better matching
	const cleanRenderedText = renderedText.trim().replace(/\s+/g, ' ');
	const cleanOriginalContent = originalContent.replace(/\s+/g, ' ');

	// Find the text in the original content
	const sourceStart = cleanOriginalContent.indexOf(cleanRenderedText);
	if (sourceStart === -1) {
		// Fallback: try to find a substantial portion of the text
		const words = cleanRenderedText.split(' ');
		if (words.length > 1) {
			// Try with first and last few words
			const firstPart = words.slice(0, Math.min(3, words.length)).join(' ');
			const lastPart = words.slice(-Math.min(3, words.length)).join(' ');
			const pattern = firstPart + '.*?' + lastPart.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
			const regex = new RegExp(pattern, 'i');
			const match = cleanOriginalContent.match(regex);
			if (match) {
				return {
					startOffset: match.index!,
					endOffset: match.index! + match[0].length
				};
			}
		}
		return null;
	}

	return {
		startOffset: sourceStart,
		endOffset: sourceStart + cleanRenderedText.length
	};
}

/**
 * Normalize text for mapping between source and rendered content
 */
function normalizeForMapping(text: string): string {
	return text
		.trim()
		// Normalize whitespace
		.replace(/\s+/g, ' ')
		// Remove markdown formatting that gets processed away
		.replace(/\*\*(.*?)\*\*/g, '$1') // Bold
		.replace(/\*(.*?)\*/g, '$1') // Italic
		.replace(/`(.*?)`/g, '$1') // Code spans
		// Normalize bullet points
		.replace(/^[•·‣⁃-]\s*/gm, '• ');
}

/**
 * Find the intersection between two text strings
 */
function findTextIntersection(text1: string, text2: string): {
	startInFirst: number;
	endInFirst: number;
	startInSecond: number;
	endInSecond: number;
} | null {
	// Find the longest common substring
	let maxLength = 0;
	let startInFirst = 0;
	let startInSecond = 0;

	for (let i = 0; i < text1.length; i++) {
		for (let j = 0; j < text2.length; j++) {
			let length = 0;
			while (
				i + length < text1.length &&
				j + length < text2.length &&
				text1[i + length].toLowerCase() === text2[j + length].toLowerCase()
			) {
				length++;
			}

			if (length > maxLength) {
				maxLength = length;
				startInFirst = i;
				startInSecond = j;
			}
		}
	}

	if (maxLength > 3) { // Only consider meaningful intersections
		return {
			startInFirst: startInFirst,
			endInFirst: startInFirst + maxLength,
			startInSecond: startInSecond,
			endInSecond: startInSecond + maxLength
		};
	}

	return null;
}

/**
 * Create component mappings for a source highlight
 */
export function createComponentMappings(
	sourceHighlight: SourceHighlight,
	tokens: Token[],
	responseId: string
): ComponentMapping[] {
	const mappings: ComponentMapping[] = [];

	// Build a more accurate source offset mapping by processing the original content
	const sourceText = sourceHighlight.originalContent;
	const highlightStart = sourceHighlight.sourceStartOffset;
	const highlightEnd = sourceHighlight.sourceEndOffset;
	const highlightedText = sourceText.substring(highlightStart, highlightEnd);

	// Process tokens recursively to handle nested structures
	function processTokens(tokenList: Token[], parentId: string = responseId, depth: number = 0): void {
		tokenList.forEach((token, tokenIndex) => {
			const componentId = `${parentId}-${tokenIndex}`;

			// Extract text content from this token
			const tokenText = extractTokenText(token);
			if (!tokenText.trim()) return;

			// Find where this token's text appears in the highlighted text
			const normalizedTokenText = tokenText.trim().replace(/\s+/g, ' ');
			const normalizedHighlightText = highlightedText.trim().replace(/\s+/g, ' ');

			// Check if this token contains part of the highlighted text
			const tokenInHighlight = normalizedHighlightText.includes(normalizedTokenText);
			const highlightInToken = normalizedTokenText.includes(normalizedHighlightText);

			if (tokenInHighlight || highlightInToken) {
				// Find the overlap between the token text and highlighted text
				let relativeStart = 0;
				let relativeEnd = tokenText.length;
				let componentText = tokenText;

				if (tokenInHighlight && !highlightInToken) {
					// Token is completely within the highlight
					relativeStart = 0;
					relativeEnd = tokenText.length;
					componentText = tokenText;
				} else if (highlightInToken && !tokenInHighlight) {
					// Highlight is completely within the token
					const startIndex = normalizedTokenText.indexOf(normalizedHighlightText);
					if (startIndex !== -1) {
						relativeStart = startIndex;
						relativeEnd = startIndex + normalizedHighlightText.length;
						componentText = tokenText.substring(relativeStart, relativeEnd);
					}
				} else {
					// Partial overlap - find the intersection
					const intersection = findTextIntersection(normalizedTokenText, normalizedHighlightText);
					if (intersection) {
						relativeStart = intersection.startInFirst;
						relativeEnd = intersection.endInFirst;
						componentText = tokenText.substring(relativeStart, relativeEnd);
					}
				}

				if (componentText.trim()) {
					mappings.push({
						componentId: componentId,
						tokenIndex: tokenIndex,
						tokenType: token.type,
						relativeStartOffset: relativeStart,
						relativeEndOffset: relativeEnd,
						componentText: componentText.trim()
					});
				}
			}

			// Process nested tokens (for paragraphs, list items, etc.)
			if (token.tokens && Array.isArray(token.tokens)) {
				processTokens(token.tokens, componentId, depth + 1);
			}
		});
	}

	processTokens(tokens);
	return mappings;
}

/**
 * Extract text content from a token recursively
 */
function extractTokenText(token: Token): string {
	// Handle different token types
	if (token.type === 'text') {
		return token.text || '';
	} else if (token.type === 'escape') {
		return token.text || '';
	} else if (token.type === 'codespan') {
		return token.text || '';
	} else if (token.type === 'br') {
		return '\n';
	} else if (token.type === 'space') {
		return ' ';
	} else if (token.tokens && Array.isArray(token.tokens)) {
		// Recursively extract text from nested tokens
		return token.tokens.map(t => extractTokenText(t)).join('');
	} else if ('text' in token && token.text) {
		return token.text;
	} else if ('raw' in token && token.raw) {
		return token.raw;
	}

	return '';
}

/**
 * Check if a component should be highlighted based on source mappings
 */
export function shouldComponentBeHighlighted(
	componentId: string,
	componentText: string,
	sourceHighlight: any
): ComponentMapping | null {
	if (!sourceHighlight.componentMappings) {
		return null;
	}

	// Find the mapping for this component
	// Try exact match first, then partial matches
	let mapping = sourceHighlight.componentMappings.find(m => m.componentId === componentId);

	if (!mapping) {
		// Try partial matches - component ID might have additional suffixes
		mapping = sourceHighlight.componentMappings.find(m =>
			componentId.includes(m.componentId) || m.componentId.includes(componentId)
		);
	}

	if (mapping) {
		// Verify the text content matches what we expect
		const normalizedComponentText = componentText.trim().replace(/\s+/g, ' ');
		const normalizedMappingText = mapping.componentText.trim().replace(/\s+/g, ' ');

		// Check if the mapping text is contained in the component text
		if (normalizedComponentText.includes(normalizedMappingText)) {
			// Adjust the mapping offsets to match the actual position in the component
			const actualStartOffset = normalizedComponentText.indexOf(normalizedMappingText);
			if (actualStartOffset !== -1) {
				return {
					...mapping,
					relativeStartOffset: actualStartOffset,
					relativeEndOffset: actualStartOffset + normalizedMappingText.length
				};
			}
		}

		// Fallback: if the expected text matches exactly at the specified offsets
		if (mapping.relativeStartOffset < componentText.length && mapping.relativeEndOffset <= componentText.length) {
			const expectedText = componentText.substring(
				mapping.relativeStartOffset,
				mapping.relativeEndOffset
			).trim().replace(/\s+/g, ' ');

			if (expectedText === normalizedMappingText) {
				return mapping;
			}
		}
	}

	return null;
}

/**
 * Get the tokens for a given content (same processing as the main pipeline)
 */
export function getTokensForContent(content: string, sourceIds: string[] = [], modelName?: string, userName?: string): Token[] {
	const processedContent = replaceTokens(processResponseContent(content), sourceIds, modelName, userName);
	return marked.lexer(processedContent);
}
